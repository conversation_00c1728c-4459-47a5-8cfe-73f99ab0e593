#!/usr/bin/env python
"""
Test script for Enhanced RAG Features

This script tests the three key enhancements:
1. Query expansion using LlamaIndex's HyDEQueryTransform
2. Multi-step reasoning using SubQuestionQueryEngine and MultiStepQueryEngine
3. Native hybrid search using LlamaIndex's hybrid retrievers

Usage:
    python test_enhanced_rag_features.py
"""

import os
import sys
import django
import logging
import time
from typing import Dict, Any

# Setup Django
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.search.services.enhanced_rag_service import EnhancedRAGService
from apps.search.services.unified_rag_service import UnifiedRAGService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_query_expansion():
    """Test HyDE query expansion feature."""
    print("🔍 Testing Query Expansion with HyDE...")
    
    try:
        # Get test tenant and user
        tenant = Tenant.objects.first()
        user = User.objects.first()
        
        if not tenant or not user:
            print("❌ No tenant or user found. Please ensure data is ingested.")
            return False
        
        # Create enhanced RAG service
        enhanced_service = EnhancedRAGService(tenant.slug, user)
        
        # Test query
        query = "How do we handle authentication in the codebase?"
        
        print(f"Original query: {query}")
        
        # Test with query expansion
        start_time = time.time()
        result, docs = enhanced_service.search(
            query_text=query,
            top_k=5,
            use_query_expansion=True,
            use_multi_step_reasoning=False,
            use_hybrid_search=True
        )
        processing_time = time.time() - start_time
        
        print(f"✅ Query expansion completed in {processing_time:.2f}s")
        print(f"Generated answer: {result.generated_answer[:200]}...")
        print(f"Retrieved {len(docs)} documents")
        
        # Check stats
        stats = enhanced_service.get_stats()
        print(f"Query expansions used: {stats['query_expansions_used']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Query expansion test failed: {e}")
        return False


def test_multi_step_reasoning():
    """Test multi-step reasoning with sub-questions."""
    print("\n🧠 Testing Multi-Step Reasoning...")
    
    try:
        # Get test tenant and user
        tenant = Tenant.objects.first()
        user = User.objects.first()
        
        # Create enhanced RAG service
        enhanced_service = EnhancedRAGService(tenant.slug, user)
        
        # Complex query that benefits from decomposition
        query = "What are the main issues discussed in engineering channels and how were they resolved?"
        
        print(f"Complex query: {query}")
        
        # Test with sub-question reasoning
        start_time = time.time()
        result, docs = enhanced_service.search(
            query_text=query,
            top_k=8,
            use_query_expansion=True,
            use_multi_step_reasoning=True,
            use_hybrid_search=True,
            reasoning_mode="sub_question"
        )
        processing_time = time.time() - start_time
        
        print(f"✅ Sub-question reasoning completed in {processing_time:.2f}s")
        print(f"Generated answer: {result.generated_answer[:300]}...")
        print(f"Retrieved {len(docs)} documents")
        
        # Test with multi-step reasoning
        start_time = time.time()
        result2, docs2 = enhanced_service.search(
            query_text=query,
            top_k=8,
            use_query_expansion=True,
            use_multi_step_reasoning=True,
            use_hybrid_search=True,
            reasoning_mode="multi_step"
        )
        processing_time2 = time.time() - start_time
        
        print(f"✅ Multi-step reasoning completed in {processing_time2:.2f}s")
        print(f"Generated answer: {result2.generated_answer[:300]}...")
        print(f"Retrieved {len(docs2)} documents")
        
        # Check stats
        stats = enhanced_service.get_stats()
        print(f"Multi-step queries: {stats['multi_step_queries']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Multi-step reasoning test failed: {e}")
        return False


def test_hybrid_search():
    """Test native LlamaIndex hybrid search."""
    print("\n🔄 Testing Native Hybrid Search...")
    
    try:
        # Get test tenant and user
        tenant = Tenant.objects.first()
        user = User.objects.first()
        
        # Create enhanced RAG service
        enhanced_service = EnhancedRAGService(tenant.slug, user)
        
        # Query that benefits from both semantic and keyword search
        query = "API authentication token validation"
        
        print(f"Hybrid search query: {query}")
        
        # Test with hybrid search enabled
        start_time = time.time()
        result, docs = enhanced_service.search(
            query_text=query,
            top_k=10,
            use_query_expansion=False,
            use_multi_step_reasoning=False,
            use_hybrid_search=True
        )
        processing_time = time.time() - start_time
        
        print(f"✅ Hybrid search completed in {processing_time:.2f}s")
        print(f"Generated answer: {result.generated_answer[:200]}...")
        print(f"Retrieved {len(docs)} documents")
        
        # Check stats
        stats = enhanced_service.get_stats()
        print(f"Hybrid searches: {stats['hybrid_searches']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Hybrid search test failed: {e}")
        return False


def test_unified_service_delegation():
    """Test that unified service properly delegates to enhanced service."""
    print("\n🔗 Testing Unified Service Delegation...")
    
    try:
        # Get test tenant and user
        tenant = Tenant.objects.first()
        user = User.objects.first()
        
        # Create unified RAG service
        unified_service = UnifiedRAGService(tenant.slug, user)
        
        # Test query
        query = "What are the recent code changes?"
        
        print(f"Delegation test query: {query}")
        
        # Test delegation when advanced features are requested
        start_time = time.time()
        result, docs = unified_service.search(
            query_text=query,
            top_k=5,
            use_query_expansion=True,  # This should trigger delegation
            use_multi_step_reasoning=False,
            use_hybrid_search=True
        )
        processing_time = time.time() - start_time
        
        print(f"✅ Delegation completed in {processing_time:.2f}s")
        print(f"Generated answer: {result.generated_answer[:200]}...")
        print(f"Retrieved {len(docs)} documents")
        
        return True
        
    except Exception as e:
        print(f"❌ Delegation test failed: {e}")
        return False


def test_performance_comparison():
    """Compare performance between standard and enhanced search."""
    print("\n⚡ Testing Performance Comparison...")
    
    try:
        # Get test tenant and user
        tenant = Tenant.objects.first()
        user = User.objects.first()
        
        # Create services
        unified_service = UnifiedRAGService(tenant.slug, user)
        enhanced_service = EnhancedRAGService(tenant.slug, user)
        
        query = "How do we implement error handling?"
        
        # Test standard search
        start_time = time.time()
        result1, docs1 = unified_service.search(
            query_text=query,
            top_k=5,
            use_query_expansion=False,
            use_multi_step_reasoning=False,
            use_hybrid_search=False
        )
        standard_time = time.time() - start_time
        
        # Test enhanced search
        start_time = time.time()
        result2, docs2 = enhanced_service.search(
            query_text=query,
            top_k=5,
            use_query_expansion=True,
            use_multi_step_reasoning=True,
            use_hybrid_search=True
        )
        enhanced_time = time.time() - start_time
        
        print(f"Standard search: {standard_time:.2f}s, {len(docs1)} results")
        print(f"Enhanced search: {enhanced_time:.2f}s, {len(docs2)} results")
        print(f"Performance overhead: {((enhanced_time - standard_time) / standard_time * 100):.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance comparison failed: {e}")
        return False


def run_all_tests():
    """Run all enhanced RAG feature tests."""
    print("🚀 Starting Enhanced RAG Features Test Suite...\n")
    
    tests = [
        ("Query Expansion", test_query_expansion),
        ("Multi-Step Reasoning", test_multi_step_reasoning),
        ("Hybrid Search", test_hybrid_search),
        ("Unified Service Delegation", test_unified_service_delegation),
        ("Performance Comparison", test_performance_comparison),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
        print()  # Add spacing between tests
    
    # Print summary
    print("📊 Test Results Summary:")
    print("=" * 50)
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All enhanced RAG features are working correctly!")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")


if __name__ == "__main__":
    run_all_tests()
