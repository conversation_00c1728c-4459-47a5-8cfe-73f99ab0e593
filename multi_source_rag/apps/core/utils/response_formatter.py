"""
Response formatting utilities for the Multi-Source RAG system.

This module provides functions for formatting RAG responses to be more human-friendly
and visually appealing.
"""

import logging
import re
from typing import Any, List, Optional

logger = logging.getLogger(__name__)


def format_response(
    response: str,
    output_format: str = "text",
    source_nodes: Optional[List[Any]] = None
) -> str:
    """
    Format a RAG response to be more human-friendly.

    Args:
        response: The raw response from the LLM
        output_format: Output format ('text', 'json', 'markdown', 'table')
        source_nodes: Source nodes for additional context (optional)

    Returns:
        str: Formatted response
    """
    # Apply a series of formatting improvements
    formatted = response

    # 1. Improve citation formatting
    formatted = format_citations(formatted)

    # 2. Enhance paragraph structure
    formatted = enhance_paragraphs(formatted)

    # 3. Improve list formatting
    formatted = enhance_lists(formatted)

    # 4. Format code blocks
    formatted = format_code_blocks(formatted)

    # 5. Add emphasis to key points
    formatted = add_emphasis(formatted)

    # Log the formatted response for debugging
    logger.debug(f"Formatted response: {formatted[:200]}...")

    return formatted


def format_citations(response: str) -> str:
    """
    Improve citation formatting in the response for a more polished, professional look.

    Args:
        response: The response text

    Returns:
        str: Response with improved citation formatting
    """
    # First handle [Document X] citations
    citation_pattern = r"\[Document (\d+)\]"
    response = re.sub(
        citation_pattern, lambda m: f"<sup>[{m.group(1)}]</sup>", response
    )

    # Then handle references to "Document X" in text
    reference_pattern = r"\b(Document|Source|source)\s+(\d+)\b"
    response = re.sub(
        reference_pattern, lambda m: f"<span class=\"source-reference\">source</span> <sup>[{m.group(2)}]</sup>", response
    )

    # Handle numeric citations like [1], [2], etc.
    numeric_citation_pattern = r"\[(\d+)\](?!\])"  # Match [1] but not [[1]]
    response = re.sub(
        numeric_citation_pattern, lambda m: f"<sup>[{m.group(1)}]</sup>", response
    )

    # Handle source references with <sup><sup>[1]</sup></sup> format (double sup)
    double_sup_pattern = r"<sup><sup>\[(\d+)\]</sup></sup>"
    response = re.sub(
        double_sup_pattern, lambda m: f"<sup>[{m.group(1)}]</sup>", response
    )

    # Clean up any "source source" duplications that might have been created
    response = re.sub(
        r"<span class=\"source-reference\">source</span>\s+<span class=\"source-reference\">source</span>",
        "<span class=\"source-reference\">source</span>",
        response
    )

    # Clean up the References section if it exists
    references_pattern = (
        r"(\*\*References:\*\*|\bReferences:|\bSources:)(.*?)(?=\n\n|\Z)"
    )

    def clean_references(match):
        refs_header = match.group(1)
        refs_content = match.group(2)

        # Remove chunk IDs and clean up formatting
        cleaned_refs = re.sub(r"Chunk Id: [a-f0-9-]+", "", refs_content)
        cleaned_refs = re.sub(r"Document Id: \d+", "", cleaned_refs)
        cleaned_refs = re.sub(r"Created At: [^,]+,?\s*", "", cleaned_refs)
        cleaned_refs = re.sub(r"\[Document (\d+):", r"<sup>[\1]</sup>", cleaned_refs)

        # Format the references section in a more readable way
        cleaned_refs = re.sub(r"<sup>\[(\d+)\]</sup>\s*(.+?)(?=<sup>|\Z)",
                             r"<div class='reference-item'><sup>[\1]</sup> \2</div>",
                             cleaned_refs)

        return f"<div class='references-section'><h6>{refs_header}</h6>{cleaned_refs}</div>"

    response = re.sub(references_pattern, clean_references, response, flags=re.DOTALL)

    return response


def enhance_paragraphs(response: str) -> str:
    """
    Enhance paragraph structure for better readability and a more polished appearance.

    Args:
        response: The response text

    Returns:
        str: Response with enhanced paragraph structure
    """
    # Ensure paragraphs have proper spacing
    paragraphs = response.split("\n\n")

    # Process each paragraph
    for i in range(len(paragraphs)):
        # Skip list items, code blocks, and references section
        if (
            paragraphs[i]
            and not paragraphs[i].startswith("- ")
            and not paragraphs[i].startswith("* ")
            and not paragraphs[i].startswith("```")
            and not paragraphs[i].startswith("<div class='references-section'>")
        ):
            # Clean up the paragraph
            paragraphs[i] = paragraphs[i].strip()

            # Add paragraph styling for better readability
            if len(paragraphs[i]) > 20:  # Only format substantial paragraphs
                # Wrap in a div with paragraph styling
                paragraphs[i] = f'<div class="enhanced-paragraph">{paragraphs[i]}</div>'

            # Add emphasis to the first sentence of key paragraphs
            if i == 0 or (i == 1 and len(paragraphs[0]) < 50):
                # Find the first sentence
                first_sentence_match = re.match(r'^(.*?[.!?])\s', paragraphs[i])
                if first_sentence_match:
                    first_sentence = first_sentence_match.group(1)
                    rest_of_paragraph = paragraphs[i][len(first_sentence):]
                    # Add subtle emphasis to the first sentence
                    paragraphs[i] = f'<div class="enhanced-paragraph"><strong>{first_sentence}</strong>{rest_of_paragraph}</div>'

    # Join paragraphs with proper spacing
    return "\n\n".join(paragraphs)


def enhance_lists(response: str) -> str:
    """
    Improve list formatting for better readability.

    Args:
        response: The response text

    Returns:
        str: Response with enhanced list formatting
    """
    # Identify list items
    lines = response.split("\n")
    in_list = False

    for i in range(len(lines)):
        # Check if line is a list item
        if re.match(r"^\s*[-*]\s", lines[i]):
            # This is a list item
            if not in_list:
                # Start of a new list, add spacing before it
                if i > 0 and lines[i - 1].strip():
                    lines[i] = "\n" + lines[i]
                in_list = True
        else:
            # Not a list item
            if in_list and lines[i].strip():
                # End of a list, add spacing after it
                lines[i] = "\n" + lines[i]
            in_list = False

    return "\n".join(lines)


def format_code_blocks(response: str) -> str:
    """
    Format code blocks for better readability.

    Args:
        response: The response text

    Returns:
        str: Response with formatted code blocks
    """
    # Identify code blocks with triple backticks
    code_block_pattern = r"```(?:\w+)?\n(.*?)```"

    def code_block_formatter(match):
        code = match.group(1)
        # Add syntax highlighting class
        return f"```\n{code}```"

    # Format code blocks with regex
    formatted = re.sub(
        code_block_pattern, code_block_formatter, response, flags=re.DOTALL
    )

    # Also format inline code
    inline_code_pattern = r"`([^`]+)`"
    formatted = re.sub(inline_code_pattern, r"`\1`", formatted)

    return formatted


def add_emphasis(response: str) -> str:
    """
    Add emphasis to key points in the response.

    Args:
        response: The response text

    Returns:
        str: Response with added emphasis
    """
    # Identify key phrases to emphasize
    emphasis_phrases = [
        "important to note",
        "key point",
        "critical",
        "essential",
        "significant",
        "best practice",
        "recommended",
    ]

    # Add emphasis to these phrases
    for phrase in emphasis_phrases:
        pattern = re.compile(r"\b" + re.escape(phrase) + r"\b", re.IGNORECASE)
        replacement = r"**\g<0>**"  # Bold the matched phrase
        response = pattern.sub(replacement, response)

    return response


def humanize_response(response: str, query_type: Optional[str] = None) -> str:
    """
    Transform the response into a more conversational, human-friendly format
    with natural language, varied phrasing, and appropriate tone.

    Args:
        response: The response text
        query_type: Type of query (factual, procedural, analytical, code, conceptual, general)

    Returns:
        str: A polished, human-friendly response
    """
    import random

    # Check if this is a "no information" response and handle it specially
    no_info_indicators = [
        "couldn't find any relevant information",
        "couldn't find specific information",
        "no direct mention",
        "no information",
        "no relevant documents",
        "no relevant information",
        "i don't have enough information",
        "i don't have information",
        "i don't have any information",
        "i don't have specific information",
        "i couldn't find",
        "no data available",
        "no documents found",
    ]

    if any(indicator in response.lower() for indicator in no_info_indicators):
        return _format_no_information_response(response)

    # Detect query type if not provided
    if not query_type:
        query_type = _detect_query_type(response)

    # Define conversational openings by query type
    openings = {
        "factual": [
            "<p class='response-intro'>Here's what I found:</p>",
            "<p class='response-intro'>Based on the available information:</p>",
            "<p class='response-intro'>According to the documentation:</p>",
            "<p class='response-intro'>The data shows that:</p>",
            "<p class='response-intro'>From what I can gather:</p>",
        ],
        "procedural": [
            "<p class='response-intro'>Here's how to approach this:</p>",
            "<p class='response-intro'>Follow these steps:</p>",
            "<p class='response-intro'>Here's a straightforward way to do this:</p>",
            "<p class='response-intro'>I'd recommend this process:</p>",
            "<p class='response-intro'>Here's the most effective approach:</p>",
        ],
        "analytical": [
            "<p class='response-intro'>After analyzing the available information:</p>",
            "<p class='response-intro'>Looking at this analytically:</p>",
            "<p class='response-intro'>From my analysis:</p>",
            "<p class='response-intro'>Considering all factors:</p>",
            "<p class='response-intro'>Taking a comprehensive view:</p>",
        ],
        "code": [
            "<p class='response-intro'>Here's the code solution:</p>",
            "<p class='response-intro'>This implementation should work well:</p>",
            "<p class='response-intro'>Here's how to code this:</p>",
            "<p class='response-intro'>The following code addresses your needs:</p>",
            "<p class='response-intro'>This implementation is clean and efficient:</p>",
        ],
        "conceptual": [
            "<p class='response-intro'>Let me explain this concept:</p>",
            "<p class='response-intro'>To understand this better:</p>",
            "<p class='response-intro'>This concept works as follows:</p>",
            "<p class='response-intro'>Here's a clear explanation:</p>",
            "<p class='response-intro'>The key idea here is:</p>",
        ],
        "general": [
            "<p class='response-intro'>Here's what you need to know:</p>",
            "<p class='response-intro'>I found some relevant information:</p>",
            "<p class='response-intro'>Here's a helpful answer:</p>",
            "<p class='response-intro'>Based on the context:</p>",
            "<p class='response-intro'>I can provide this information:</p>",
        ],
    }

    # Define conversational closings by query type
    closings = {
        "factual": [
            "<p class='response-closing'>Does this information help with what you were looking for?</p>",
            "<p class='response-closing'>Let me know if you need any clarification on these facts.</p>",
            "<p class='response-closing'>Is there anything specific from this information you'd like me to elaborate on?</p>",
            "<p class='response-closing'>Would you like me to find more details on any particular aspect?</p>",
        ],
        "procedural": [
            "<p class='response-closing'>Would you like me to explain any of these steps in more detail?</p>",
            "<p class='response-closing'>Let me know how this process works for you.</p>",
            "<p class='response-closing'>Is there any part of this process you'd like me to clarify?</p>",
            "<p class='response-closing'>Do these steps address what you were trying to accomplish?</p>",
        ],
        "analytical": [
            "<p class='response-closing'>Would you like me to explore any aspect of this analysis further?</p>",
            "<p class='response-closing'>Does this analysis address your question effectively?</p>",
            "<p class='response-closing'>Let me know if you'd like me to consider additional factors in this analysis.</p>",
            "<p class='response-closing'>Is there a specific part of this analysis you'd like me to expand on?</p>",
        ],
        "code": [
            "<p class='response-closing'>Would you like me to explain how any part of this code works?</p>",
            "<p class='response-closing'>Let me know if you need any adjustments to this implementation.</p>",
            "<p class='response-closing'>Is there anything about this code solution that needs clarification?</p>",
            "<p class='response-closing'>Would you like me to suggest any optimizations for this code?</p>",
        ],
        "conceptual": [
            "<p class='response-closing'>Does this explanation help clarify the concept?</p>",
            "<p class='response-closing'>Would you like me to provide examples to illustrate this concept?</p>",
            "<p class='response-closing'>Is there any aspect of this concept you'd like me to explain differently?</p>",
            "<p class='response-closing'>Let me know if this explanation makes sense to you.</p>",
        ],
        "general": [
            "<p class='response-closing'>Is there anything else you'd like to know?</p>",
            "<p class='response-closing'>Does this answer your question?</p>",
            "<p class='response-closing'>Let me know if you need any additional information.</p>",
            "<p class='response-closing'>Would you like me to explore any related topics?</p>",
        ],
    }

    # Clean up the response before adding conversational elements
    response = _clean_response_text(response)

    # Select appropriate opening and closing
    opening = random.choice(openings.get(query_type, openings["general"]))
    closing = random.choice(closings.get(query_type, closings["general"]))

    # Check if the response already has an opening phrase
    has_opening = False
    for op_list in openings.values():
        for op in op_list:
            clean_op = re.sub(r'<[^>]+>', '', op).lower()
            if response.lower().startswith(clean_op):
                has_opening = True
                break
        if has_opening:
            break

    # Add opening if needed
    if not has_opening:
        response = f"{opening} {response}"

    # Add closing if appropriate (for longer responses)
    if len(response) > 100 and not response.endswith(("!", "?")):
        # Make sure we don't add a closing if there's already one
        has_closing = False
        for close_list in closings.values():
            for close in close_list:
                clean_close = re.sub(r'<[^>]+>', '', close).lower()
                if response.lower().endswith(clean_close.lower()):
                    has_closing = True
                    break
            if has_closing:
                break

        if not has_closing:
            response = f"{response}\n\n{closing}"

    # Wrap the entire response in a container for styling
    response = f'<div class="humanized-response">{response}</div>'

    return response


def _detect_query_type(response: str) -> str:
    """
    Detect the type of query based on the response content.

    Args:
        response: The response text

    Returns:
        str: Detected query type
    """
    # Look for indicators in the response
    response_lower = response.lower()

    # Check for code blocks
    if "```" in response or "<code>" in response or "function" in response_lower or "class" in response_lower:
        return "code"

    # Check for procedural content
    if any(marker in response_lower for marker in ["step", "steps", "first", "then", "finally", "process", "procedure", "how to"]):
        return "procedural"

    # Check for analytical content
    if any(marker in response_lower for marker in ["analysis", "analyze", "consider", "comparison", "evaluate", "pros and cons"]):
        return "analytical"

    # Check for conceptual content
    if any(marker in response_lower for marker in ["concept", "understand", "principle", "theory", "framework", "idea"]):
        return "conceptual"

    # Check for factual content
    if any(marker in response_lower for marker in ["fact", "data", "information", "according to", "research", "study"]):
        return "factual"

    # Default to general
    return "general"


def _format_no_information_response(response: str) -> str:
    """
    Format a response when no relevant information was found.

    Args:
        response: The original "no information" response

    Returns:
        str: A more helpful and user-friendly "no information" response
    """
    import random

    # Extract any specific term that might be mentioned in the response
    term_match = re.search(r"about\s+['\"]?([^'\".,]+)['\"]?", response)
    term = term_match.group(1) if term_match else "your query"

    # Check if there's a fallback explanation in the response
    has_fallback = "general explanation" in response.lower() or "while i couldn't find" in response.lower()

    if has_fallback:
        # If there's already a fallback explanation, just wrap it nicely
        return f'<div class="humanized-response">{response}</div>'

    # Different ways to acknowledge the lack of information
    acknowledgments = [
        f"<p class='response-intro'>I've searched the available documents, but:</p>",
        f"<p class='response-intro'>After reviewing the available information:</p>",
        f"<p class='response-intro'>I've checked the knowledge base, but:</p>",
    ]

    # Different suggestions for the user
    suggestions = [
        "<div class='enhanced-paragraph'>To get better results, you might want to:</div><ul><li>Try rephrasing your question</li><li>Use more specific terms related to the documents in the system</li><li>Ask about a different but related topic</li></ul>",
        "<div class='enhanced-paragraph'>Here are some options to explore:</div><ul><li>Consider using different keywords in your query</li><li>Try asking about related concepts that might be covered in the documents</li><li>Specify a particular aspect you're interested in</li></ul>",
        "<div class='enhanced-paragraph'>To help me find relevant information:</div><ul><li>Try using terminology that might appear in the documents</li><li>Consider narrowing your question to a specific aspect</li><li>Ask about related topics that might be covered</li></ul>",
    ]

    # Different closing statements
    closings = [
        "<p class='response-closing'>Is there something else I can help you with?</p>",
        "<p class='response-closing'>Would you like to try a different approach to your question?</p>",
        "<p class='response-closing'>Is there a related topic you'd like to explore instead?</p>",
    ]

    # Construct the response
    formatted_response = f"""<div class="humanized-response">
    {random.choice(acknowledgments)}
    <div class="enhanced-paragraph"><strong>I couldn't find specific information about {term} in the available documents.</strong></div>
    {random.choice(suggestions)}
    {random.choice(closings)}
</div>"""

    return formatted_response

def _clean_response_text(response: str) -> str:
    """
    Clean up the response text by removing redundant phrases and formatting issues.

    Args:
        response: The response text

    Returns:
        str: Cleaned response text
    """
    # Remove common LLM prefixes
    prefixes_to_remove = [
        "I'll answer based on the provided context.",
        "Based on the provided context,",
        "According to the provided information,",
        "From the information provided,",
        "Based on the available information:",
        "Here's what I found:",
        "According to the documents:",
    ]

    for prefix in prefixes_to_remove:
        if response.startswith(prefix):
            response = response[len(prefix):].strip()

    # Remove redundant phrases
    redundant_phrases = [
        "I hope this helps!",
        "Let me know if you have any other questions!",
        "Is there anything else you'd like to know?",
        "Let me know if you need any clarification.",
        "I hope this information is helpful!",
        "Let me know if you have any follow-up questions.",
    ]

    for phrase in redundant_phrases:
        response = response.replace(phrase, "")
        response = response.replace(phrase.lower(), "")

    # Clean up any double spaces or excessive newlines
    response = re.sub(r'\s{2,}', ' ', response)
    response = re.sub(r'\n{3,}', '\n\n', response)

    return response.strip()