"""
LlamaIndex setup utilities.

This module provides utilities for setting up LlamaIndex components.
"""

import logging
from typing import Any, Optional

logger = logging.getLogger(__name__)


class LlamaIndexRegistry:
    """Registry for LlamaIndex components."""

    def __init__(self):
        """Initialize the registry."""
        self._embeddings = {}
        self._llms = {}
        self._vector_stores = {}
        self._node_parsers = {}
        self._service_contexts = {}
        self._default_embedding = None
        self._default_llm = None
        self._default_vector_store = None
        self._default_node_parser = None
        self._default_service_context = None

    # Embedding methods
    def register_embedding(self, name: str, embedding: Any) -> None:
        """Register an embedding model."""
        self._embeddings[name] = embedding
        if self._default_embedding is None:
            self._default_embedding = name

    def get_embedding(self, name: Optional[str] = None) -> Any:
        """Get an embedding model by name or the default."""
        if name is None:
            name = self._default_embedding
        return self._embeddings.get(name)

    def set_default_embedding(self, name: str) -> None:
        """Set the default embedding model."""
        if name in self._embeddings:
            self._default_embedding = name

    # LLM methods
    def register_llm(self, name: str, llm: Any) -> None:
        """Register an LLM."""
        self._llms[name] = llm
        if self._default_llm is None:
            self._default_llm = name

    def get_llm(self, name: Optional[str] = None) -> Any:
        """Get an LLM by name or the default."""
        if name is None:
            name = self._default_llm
        return self._llms.get(name)

    def set_default_llm(self, name: str) -> None:
        """Set the default LLM."""
        if name in self._llms:
            self._default_llm = name

    # Vector store methods
    def register_vector_store(self, name: str, vector_store: Any) -> None:
        """Register a vector store."""
        self._vector_stores[name] = vector_store
        if self._default_vector_store is None:
            self._default_vector_store = name

    def get_vector_store(self, name: Optional[str] = None) -> Any:
        """Get a vector store by name or the default."""
        if name is None:
            name = self._default_vector_store
        return self._vector_stores.get(name)

    def set_default_vector_store(self, name: str) -> None:
        """Set the default vector store."""
        if name in self._vector_stores:
            self._default_vector_store = name

    # Node parser methods
    def register_node_parser(self, name: str, node_parser: Any) -> None:
        """Register a node parser."""
        self._node_parsers[name] = node_parser
        if self._default_node_parser is None:
            self._default_node_parser = name

    def get_node_parser(self, name: Optional[str] = None) -> Any:
        """Get a node parser by name or the default."""
        if name is None:
            name = self._default_node_parser
        return self._node_parsers.get(name)

    def set_default_node_parser(self, name: str) -> None:
        """Set the default node parser."""
        if name in self._node_parsers:
            self._default_node_parser = name

    # Service context methods
    def register_service_context(self, name: str, service_context: Any) -> None:
        """Register a service context."""
        self._service_contexts[name] = service_context
        if self._default_service_context is None:
            self._default_service_context = name

    def get_service_context(self, name: Optional[str] = None) -> Any:
        """Get a service context by name or the default."""
        if name is None:
            name = self._default_service_context
        return self._service_contexts.get(name)

    def set_default_service_context(self, name: str) -> None:
        """Set the default service context."""
        if name in self._service_contexts:
            self._default_service_context = name


# Create global registry
llama_index_registry = LlamaIndexRegistry()
