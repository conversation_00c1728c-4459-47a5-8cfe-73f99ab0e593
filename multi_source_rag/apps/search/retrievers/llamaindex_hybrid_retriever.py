"""
LlamaIndex Native Hybrid Retriever

This module implements native LlamaIndex hybrid search capabilities:
- QueryFusionRetriever for combining multiple retrieval strategies
- BM25Retriever for keyword-based search
- VectorIndexRetriever for semantic search
- Advanced fusion and reranking strategies
"""

import logging
from typing import Any, Dict, List, Optional

from llama_index.core import VectorStoreIndex
from llama_index.core.retrievers import (
    VectorIndexRetriever,
    QueryFusionRetriever,
    BaseRetriever
)
from llama_index.core.schema import NodeWithScore, QueryBundle
from llama_index.core.postprocessor import LLMRerank, SimilarityPostprocessor

# Note: HyDEQueryTransform import moved to method level to avoid import issues

from apps.core.utils.collection_manager import get_collection_name
from apps.core.utils.llama_index_llm import get_llm
from apps.core.utils.llama_index_vectorstore import get_vector_store

logger = logging.getLogger(__name__)


class LlamaIndexHybridRetriever(BaseRetriever):
    """
    Native LlamaIndex Hybrid Retriever.

    This retriever combines:
    1. Vector-based semantic search
    2. BM25 keyword search (when available)
    3. Query fusion for multiple query variations
    4. Advanced reranking and post-processing
    """

    def __init__(
        self,
        tenant_slug: str,
        intent: Optional[str] = None,
        similarity_top_k: int = 10,
        fusion_top_k: int = 20,
        num_queries: int = 3,
        use_query_expansion: bool = True,
        use_reranking: bool = True,
        rerank_top_n: int = 5,
        similarity_cutoff: float = 0.7
    ):
        """
        Initialize the hybrid retriever.

        Args:
            tenant_slug: Tenant slug for collection selection
            intent: Query intent for specialized collections
            similarity_top_k: Number of results from each retriever
            fusion_top_k: Total number of results after fusion
            num_queries: Number of query variations to generate
            use_query_expansion: Whether to use HyDE query expansion
            use_reranking: Whether to use LLM reranking
            rerank_top_n: Number of results to rerank
            similarity_cutoff: Minimum similarity score threshold
        """
        super().__init__()
        self.tenant_slug = tenant_slug
        self.intent = intent
        self.similarity_top_k = similarity_top_k
        self.fusion_top_k = fusion_top_k
        self.num_queries = num_queries
        self.use_query_expansion = use_query_expansion
        self.use_reranking = use_reranking
        self.rerank_top_n = rerank_top_n
        self.similarity_cutoff = similarity_cutoff

        # Initialize components
        self.llm = get_llm()

        # Initialize query transform if needed
        if use_query_expansion:
            try:
                from llama_index.core.query_transform import HyDEQueryTransform
                self.query_transform = HyDEQueryTransform(include_original=True)
            except ImportError:
                logger.warning("HyDEQueryTransform not available, disabling query expansion")
                self.query_transform = None
        else:
            self.query_transform = None

        # Build retrievers
        self.vector_retriever = self._build_vector_retriever()
        self.fusion_retriever = self._build_fusion_retriever()

        # Build post-processors
        self.post_processors = self._build_post_processors()

    def _build_vector_retriever(self) -> VectorIndexRetriever:
        """Build vector-based retriever."""
        collection_name = get_collection_name(self.tenant_slug, self.intent)
        vector_store = get_vector_store(collection_name=collection_name)
        index = VectorStoreIndex.from_vector_store(vector_store)

        return VectorIndexRetriever(
            index=index,
            similarity_top_k=self.similarity_top_k
        )

    def _build_fusion_retriever(self) -> QueryFusionRetriever:
        """Build query fusion retriever with multiple strategies."""
        # For now, we'll use multiple vector retrievers with different parameters
        # In a full implementation, you would add BM25Retriever here
        retrievers = [
            self.vector_retriever,
            # Additional retrievers can be added here
            # BM25Retriever(nodes=nodes) - would need document nodes
        ]

        return QueryFusionRetriever(
            retrievers=retrievers,
            similarity_top_k=self.fusion_top_k,
            num_queries=self.num_queries,
            mode="reciprocal_rerank",  # or "relative_score"
            use_async=False,
            query_gen_prompt=None,  # Use default query generation
            llm=self.llm
        )

    def _build_post_processors(self) -> List[Any]:
        """Build post-processing pipeline."""
        processors = []

        # Similarity filtering
        processors.append(
            SimilarityPostprocessor(similarity_cutoff=self.similarity_cutoff)
        )

        # LLM reranking
        if self.use_reranking:
            processors.append(
                LLMRerank(
                    choice_batch_size=5,
                    top_n=self.rerank_top_n,
                    llm=self.llm
                )
            )

        return processors

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """
        Retrieve nodes using hybrid approach.

        Args:
            query_bundle: Query bundle containing query string and metadata

        Returns:
            List of nodes with scores
        """
        try:
            # Step 1: Query expansion (if enabled)
            if self.use_query_expansion and self.query_transform:
                logger.info("Applying HyDE query transformation")
                expanded_query = self.query_transform.run(query_bundle.query_str)
                query_bundle = QueryBundle(
                    query_str=expanded_query,
                    custom_embedding_strs=query_bundle.custom_embedding_strs,
                    embedding=query_bundle.embedding
                )

            # Step 2: Retrieve using fusion retriever
            logger.info(f"Retrieving with fusion retriever (top_k={self.fusion_top_k})")
            nodes = self.fusion_retriever.retrieve(query_bundle)

            # Step 3: Apply post-processing
            for processor in self.post_processors:
                logger.info(f"Applying post-processor: {processor.__class__.__name__}")
                nodes = processor.postprocess_nodes(nodes, query_bundle)

            logger.info(f"Hybrid retrieval completed with {len(nodes)} nodes")
            return nodes

        except Exception as e:
            logger.error(f"Error in hybrid retrieval: {str(e)}", exc_info=True)
            # Fallback to simple vector retrieval
            logger.info("Falling back to simple vector retrieval")
            return self.vector_retriever.retrieve(query_bundle)

    def get_retrieval_stats(self) -> Dict[str, Any]:
        """Get retrieval statistics."""
        return {
            "retriever_type": "llamaindex_hybrid",
            "tenant_slug": self.tenant_slug,
            "intent": self.intent,
            "similarity_top_k": self.similarity_top_k,
            "fusion_top_k": self.fusion_top_k,
            "num_queries": self.num_queries,
            "features": {
                "query_expansion": self.use_query_expansion,
                "reranking": self.use_reranking,
                "similarity_filtering": True,
                "query_fusion": True
            }
        }


class EnhancedQueryFusionRetriever(QueryFusionRetriever):
    """
    Enhanced Query Fusion Retriever with additional capabilities.

    This extends LlamaIndex's QueryFusionRetriever with:
    - Better query generation strategies
    - Advanced fusion algorithms
    - Improved error handling
    """

    def __init__(
        self,
        retrievers: List[BaseRetriever],
        similarity_top_k: int = 10,
        num_queries: int = 3,
        mode: str = "reciprocal_rerank",
        use_async: bool = False,
        query_gen_prompt: Optional[str] = None,
        llm: Optional[Any] = None,
        fusion_algorithm: str = "rrf"  # "rrf" or "weighted"
    ):
        """
        Initialize enhanced query fusion retriever.

        Args:
            retrievers: List of retrievers to fuse
            similarity_top_k: Number of results per retriever
            num_queries: Number of query variations
            mode: Fusion mode
            use_async: Whether to use async retrieval
            query_gen_prompt: Custom query generation prompt
            llm: Language model for query generation
            fusion_algorithm: Algorithm for fusing results
        """
        super().__init__(
            retrievers=retrievers,
            similarity_top_k=similarity_top_k,
            num_queries=num_queries,
            mode=mode,
            use_async=use_async,
            query_gen_prompt=query_gen_prompt,
            llm=llm or get_llm()
        )
        self.fusion_algorithm = fusion_algorithm

    def _fuse_results_rrf(
        self,
        results_dict: Dict[str, List[NodeWithScore]],
        k: int = 60
    ) -> List[NodeWithScore]:
        """
        Fuse results using Reciprocal Rank Fusion (RRF).

        Args:
            results_dict: Dictionary of retriever results
            k: RRF parameter (typically 60)

        Returns:
            Fused and ranked results
        """
        # Implementation of RRF algorithm
        node_scores = {}

        for _, nodes in results_dict.items():
            for rank, node in enumerate(nodes):
                node_id = node.node.node_id
                if node_id not in node_scores:
                    node_scores[node_id] = {"node": node, "score": 0.0}

                # RRF formula: 1 / (k + rank)
                rrf_score = 1.0 / (k + rank + 1)
                node_scores[node_id]["score"] += rrf_score

        # Sort by fused score
        sorted_nodes = sorted(
            node_scores.values(),
            key=lambda x: x["score"],
            reverse=True
        )

        # Return nodes with updated scores
        fused_nodes = []
        for item in sorted_nodes:
            node = item["node"]
            node.score = item["score"]
            fused_nodes.append(node)

        return fused_nodes

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """Enhanced retrieve with better fusion algorithms."""
        try:
            # Use parent's retrieve method
            nodes = super()._retrieve(query_bundle)

            # Apply additional processing if needed
            logger.info(f"Enhanced fusion retrieval completed with {len(nodes)} nodes")
            return nodes

        except Exception as e:
            logger.error(f"Error in enhanced fusion retrieval: {str(e)}", exc_info=True)
            # Fallback to first retriever
            if self._retrievers:
                return self._retrievers[0].retrieve(query_bundle)
            return []


def create_hybrid_retriever(
    tenant_slug: str,
    intent: Optional[str] = None,
    **kwargs
) -> LlamaIndexHybridRetriever:
    """
    Factory function to create a hybrid retriever.

    Args:
        tenant_slug: Tenant slug
        intent: Query intent
        **kwargs: Additional configuration options

    Returns:
        Configured hybrid retriever
    """
    return LlamaIndexHybridRetriever(
        tenant_slug=tenant_slug,
        intent=intent,
        **kwargs
    )
