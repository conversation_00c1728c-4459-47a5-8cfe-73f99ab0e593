"""
Enhanced RAG Service with LlamaIndex Advanced Features

This service implements the three key enhancements:
1. Query expansion using LlamaIndex's HyDEQueryTransform
2. Multi-step reasoning using SubQuestionQueryEngine and MultiStepQueryEngine
3. Native hybrid search using LlamaIndex's hybrid retrievers

Features:
- HyDE (Hypothetical Document Embeddings) query transformation
- Sub-question decomposition for complex queries
- Multi-step reasoning with iterative refinement
- Native LlamaIndex hybrid search with BM25 + vector retrieval
- Advanced query routing and response synthesis
"""

import logging
import time
from typing import Any, Dict, List, Optional, Tuple

from django.contrib.auth.models import User

# LlamaIndex core imports
from llama_index.core import VectorStoreIndex
from llama_index.core.query_engine import (
    RouterQueryEngine,
    RetrieverQueryEngine,
    CitationQueryEngine,
    SubQuestionQueryEngine,
    MultiStepQueryEngine
)
from llama_index.core.retrievers import (
    VectorIndexRetriever,
    QueryFusionRetriever
)
from llama_index.core.tools import QueryEngineTool
from llama_index.core.selectors import LLMSingleSelector
# HyDEQueryTransform import moved to method level to handle import issues
from llama_index.core.response_synthesizers import get_response_synthesizer
from llama_index.core.postprocessor import SimilarityPostprocessor, LLMRerank

# App imports
from apps.accounts.models import Tenant
from apps.core.utils.collection_manager import get_collection_name
from apps.core.utils.llama_index_llm import get_llm
from apps.core.utils.llama_index_vectorstore import get_vector_store
from apps.core.utils.response_formatter import format_response
from apps.documents.models import DocumentChunk
from apps.search.models import ResultCitation, SearchQuery, SearchResult

logger = logging.getLogger(__name__)


class EnhancedRAGService:
    """
    Enhanced RAG Service with advanced LlamaIndex features.

    This service provides:
    1. Query expansion using HyDE (Hypothetical Document Embeddings)
    2. Multi-step reasoning with sub-question decomposition
    3. Native hybrid search with BM25 + vector retrieval
    4. Advanced query routing and response synthesis
    """

    def __init__(self, tenant_slug: str, user: Optional[User] = None):
        """
        Initialize the Enhanced RAG Service.

        Args:
            tenant_slug: Tenant slug for tenant-specific operations
            user: User performing the search
        """
        self.tenant_slug = tenant_slug
        self.user = user

        # Get tenant from slug
        try:
            self.tenant = Tenant.objects.get(slug=tenant_slug)
        except Tenant.DoesNotExist:
            logger.error(f"Tenant with slug {tenant_slug} not found")
            raise ValueError(f"Tenant with slug {tenant_slug} not found")

        # Initialize components
        self.llm = get_llm()

        # Initialize query transform
        try:
            from llama_index.core.query_transform import HyDEQueryTransform
            self.query_transform = HyDEQueryTransform(include_original=True)
        except ImportError:
            logger.warning("HyDEQueryTransform not available, disabling query expansion")
            self.query_transform = None

        # Build specialized engines
        self.query_engines = self._build_query_engines()
        self.router_engine = self._build_router_engine()
        self.citation_engine = self._build_citation_engine()
        self.sub_question_engine = self._build_sub_question_engine()
        self.multi_step_engine = self._build_multi_step_engine()

        # Statistics tracking
        self.stats = {
            "queries_processed": 0,
            "avg_processing_time": 0.0,
            "query_expansions_used": 0,
            "multi_step_queries": 0,
            "hybrid_searches": 0
        }

    def _build_query_engines(self) -> Dict[str, QueryEngineTool]:
        """
        Build specialized query engines for different content types.

        Returns:
            Dictionary of query engine tools
        """
        engines = {}

        # Conversation engine with hybrid search
        engines["conversation"] = QueryEngineTool.from_defaults(
            query_engine=self._build_conversation_engine(),
            name="conversation_search",
            description="Search Slack conversations, chat messages, and discussion threads"
        )

        # Code engine with semantic search
        engines["code"] = QueryEngineTool.from_defaults(
            query_engine=self._build_code_engine(),
            name="code_search",
            description="Search GitHub repositories, code files, PRs, and issues"
        )

        # Document engine with comprehensive search
        engines["document"] = QueryEngineTool.from_defaults(
            query_engine=self._build_document_engine(),
            name="document_search",
            description="Search general documents, files, and knowledge base content"
        )

        return engines

    def _build_conversation_engine(self) -> RetrieverQueryEngine:
        """Build specialized engine for conversation content with hybrid search."""
        collection_name = get_collection_name(self.tenant_slug, intent="conversation")
        vector_store = get_vector_store(collection_name=collection_name)
        index = VectorStoreIndex.from_vector_store(vector_store)

        # Create hybrid retriever with BM25 + vector search
        vector_retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=10
        )

        # Note: BM25Retriever would need documents to be indexed
        # For now, we'll use QueryFusionRetriever with multiple vector retrievers
        hybrid_retriever = QueryFusionRetriever(
            retrievers=[vector_retriever],
            similarity_top_k=10,
            num_queries=3,  # Generate multiple query variations
            mode="reciprocal_rerank",
            use_async=False
        )

        return RetrieverQueryEngine.from_args(
            retriever=hybrid_retriever,
            llm=self.llm,
            response_synthesizer=get_response_synthesizer(
                response_mode="tree_summarize",
                llm=self.llm
            ),
            node_postprocessors=[
                SimilarityPostprocessor(similarity_cutoff=0.7),
                LLMRerank(choice_batch_size=5, top_n=5, llm=self.llm)
            ]
        )

    def _build_code_engine(self) -> RetrieverQueryEngine:
        """Build specialized engine for code content."""
        collection_name = get_collection_name(self.tenant_slug, intent="code")
        vector_store = get_vector_store(collection_name=collection_name)
        index = VectorStoreIndex.from_vector_store(vector_store)

        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=15
        )

        return RetrieverQueryEngine.from_args(
            retriever=retriever,
            llm=self.llm,
            response_synthesizer=get_response_synthesizer(
                response_mode="compact",
                llm=self.llm
            ),
            node_postprocessors=[
                SimilarityPostprocessor(similarity_cutoff=0.6)
            ]
        )

    def _build_document_engine(self) -> RetrieverQueryEngine:
        """Build specialized engine for general documents."""
        collection_name = get_collection_name(self.tenant_slug, intent="document")
        vector_store = get_vector_store(collection_name=collection_name)
        index = VectorStoreIndex.from_vector_store(vector_store)

        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=12
        )

        return RetrieverQueryEngine.from_args(
            retriever=retriever,
            llm=self.llm,
            response_synthesizer=get_response_synthesizer(
                response_mode="tree_summarize",
                llm=self.llm
            ),
            node_postprocessors=[
                SimilarityPostprocessor(similarity_cutoff=0.65)
            ]
        )

    def _build_router_engine(self) -> RouterQueryEngine:
        """Build router query engine for intelligent query routing."""
        return RouterQueryEngine(
            selector=LLMSingleSelector.from_defaults(llm=self.llm),
            query_engine_tools=list(self.query_engines.values()),
            verbose=True
        )

    def _build_citation_engine(self) -> CitationQueryEngine:
        """Build citation query engine for automatic citation tracking."""
        return CitationQueryEngine.from_args(
            query_engine=self.router_engine,
            llm=self.llm,
            citation_chunk_size=512
        )

    def _build_sub_question_engine(self) -> SubQuestionQueryEngine:
        """Build sub-question query engine for complex query decomposition."""
        return SubQuestionQueryEngine.from_defaults(
            query_engine_tools=list(self.query_engines.values()),
            llm=self.llm,
            use_async=False,
            verbose=True
        )

    def _build_multi_step_engine(self) -> MultiStepQueryEngine:
        """Build multi-step query engine for iterative reasoning."""
        return MultiStepQueryEngine(
            query_engine=self.router_engine,
            llm=self.llm,
            num_steps=3,
            early_stopping=True
        )

    def search(
        self,
        query_text: str,
        top_k: int = 20,
        metadata_filter: Optional[Dict[str, Any]] = None,
        output_format: str = "text",
        min_relevance_score: float = 0.4,
        use_query_expansion: bool = True,
        use_multi_step_reasoning: bool = False,
        use_hybrid_search: bool = True,
        reasoning_mode: str = "sub_question"  # "sub_question" or "multi_step"
    ) -> Tuple[SearchResult, List[Tuple[Any, float]]]:
        """
        Enhanced search with query expansion, multi-step reasoning, and hybrid search.

        Args:
            query_text: Query text
            top_k: Number of results to return
            metadata_filter: Filter to apply to search results
            output_format: Output format ('text', 'json', 'markdown', 'table')
            min_relevance_score: Minimum relevance score for documents
            use_query_expansion: Whether to use HyDE query expansion
            use_multi_step_reasoning: Whether to use multi-step reasoning
            use_hybrid_search: Whether to use hybrid search
            reasoning_mode: Type of reasoning ("sub_question" or "multi_step")

        Returns:
            Tuple of (SearchResult, retrieved_documents)
        """
        start_time = time.time()

        try:
            # Create search query record
            search_query = self._create_search_query(query_text, metadata_filter)

            # Step 1: Query Expansion using HyDE
            if use_query_expansion and self.query_transform:
                logger.info("Using HyDE query expansion")
                expanded_query = self.query_transform.run(query_text)
                self.stats["query_expansions_used"] += 1
            else:
                if use_query_expansion and not self.query_transform:
                    logger.warning("Query expansion requested but HyDE not available")
                expanded_query = query_text

            # Step 2: Choose reasoning approach
            if use_multi_step_reasoning:
                if reasoning_mode == "sub_question":
                    logger.info("Using sub-question reasoning")
                    response = self.sub_question_engine.query(expanded_query)
                elif reasoning_mode == "multi_step":
                    logger.info("Using multi-step reasoning")
                    response = self.multi_step_engine.query(expanded_query)
                    self.stats["multi_step_queries"] += 1
                else:
                    logger.warning(f"Unknown reasoning mode: {reasoning_mode}, using citation engine")
                    response = self.citation_engine.query(expanded_query)
            else:
                # Use citation engine for standard search
                response = self.citation_engine.query(expanded_query)

            if use_hybrid_search:
                self.stats["hybrid_searches"] += 1

            # Extract source nodes for citations
            source_nodes = response.source_nodes if hasattr(response, 'source_nodes') else []

            # Filter by relevance score
            filtered_nodes = [
                node for node in source_nodes
                if hasattr(node, 'score') and node.score >= min_relevance_score
            ][:top_k]

            # Calculate processing time
            processing_time = time.time() - start_time

            # Create search result
            search_result = self._create_search_result(
                search_query, response, filtered_nodes, output_format, processing_time
            )

            # Convert nodes to documents for backward compatibility
            retrieved_docs = self._convert_nodes_to_documents(filtered_nodes)

            # Update statistics
            self.stats["queries_processed"] += 1
            self.stats["avg_processing_time"] = (
                (self.stats["avg_processing_time"] * (self.stats["queries_processed"] - 1) + processing_time)
                / self.stats["queries_processed"]
            )

            logger.info(f"Enhanced search completed in {processing_time:.2f}s with {len(retrieved_docs)} results")
            return search_result, retrieved_docs

        except Exception as e:
            logger.error(f"Error in enhanced search: {str(e)}", exc_info=True)
            # Create fallback search result
            search_query = self._create_search_query(query_text, metadata_filter)
            processing_time = time.time() - start_time

            search_result = SearchResult.objects.create(
                search_query=search_query,
                generated_answer=f"Error processing query: {str(e)}",
                retriever_score=0.0,
                confidence_score=0.0,
                processing_time=processing_time,
                response_format=output_format
            )

            return search_result, []

    def _create_search_query(self, query_text: str, metadata_filter: Optional[Dict[str, Any]]) -> SearchQuery:
        """Create a search query record."""
        return SearchQuery.objects.create(
            user=self.user,
            tenant=self.tenant,
            query_text=query_text,
            search_params={
                "metadata_filter": metadata_filter,
                "service_type": "enhanced_rag"
            }
        )

    def _create_search_result(
        self,
        search_query: SearchQuery,
        response: Any,
        filtered_nodes: List[Any],
        output_format: str,
        processing_time: float
    ) -> SearchResult:
        """Create a search result from the LlamaIndex response."""

        # Calculate average retriever score
        retriever_score_avg = (
            sum([node.score for node in filtered_nodes if hasattr(node, 'score')]) / len(filtered_nodes)
            if filtered_nodes else 0
        )

        # Format response based on output format
        formatted_answer = format_response(
            str(response),
            output_format=output_format
        )

        # Create search result
        search_result = SearchResult.objects.create(
            search_query=search_query,
            generated_answer=formatted_answer,
            retriever_score=retriever_score_avg,
            confidence_score=min(retriever_score_avg * 1.2, 1.0),  # Boost confidence slightly
            processing_time=processing_time,
            response_format=output_format
        )

        # Create citations
        for i, node in enumerate(filtered_nodes):
            if hasattr(node, 'node') and hasattr(node.node, 'metadata'):
                metadata = node.node.metadata
                document_id = metadata.get('document_id')

                if document_id:
                    try:
                        chunk = DocumentChunk.objects.get(
                            document__id=document_id
                        )

                        ResultCitation.objects.create(
                            search_result=search_result,
                            document_chunk=chunk,
                            relevance_score=getattr(node, 'score', 0.0),
                            rank=i + 1,
                            citation_text=str(node.node.text)[:500] if hasattr(node.node, 'text') else ""
                        )
                    except DocumentChunk.DoesNotExist:
                        logger.warning(f"Document chunk not found for document_id: {document_id}")

        return search_result

    def _convert_nodes_to_documents(self, nodes: List[Any]) -> List[Tuple[Any, float]]:
        """Convert LlamaIndex nodes to documents for backward compatibility."""
        documents = []
        for node in nodes:
            if hasattr(node, 'node') and hasattr(node, 'score'):
                documents.append((node.node, node.score))
            elif hasattr(node, 'score'):
                documents.append((node, node.score))
        return documents

    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        return {
            **self.stats,
            "service_type": "enhanced_rag",
            "features_enabled": {
                "query_expansion": True,
                "multi_step_reasoning": True,
                "hybrid_search": True,
                "citation_tracking": True
            }
        }
